using Vsp.PayrollConfiguration.Domain.AbpFund.Models;
using Vsp.PayrollConfiguration.Infrastructure.Commands;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.AbpFund.Commands;

internal class PatchAbpFundCommand(
    IBaseCommandDependencies<ILoketContext> dependencies,
    IGetInheritanceEntityQuery<AbpFundModel, Repository.Entities.AbpFund> query)
    : PatchInheritanceEntityCommand<AbpFundPatchModel, AbpFundModel, Repository.Entities.AbpFund, ModelAbpFund>(dependencies, query)
{
    protected override bool UpdateOnly => false;
    private static readonly IEnumerable<(PropertyInfo Entity, PropertyInfo ModelEntity)> properties = 
        [
            ( typeof(Repository.Entities.AbpFund).GetProperty(nameof(Repository.Entities.AbpFund.TotalContribution))!, typeof(ModelAbpFund).GetProperty(nameof(ModelAbpFund.TotalContribution))! ),
            ( typeof(Repository.Entities.AbpFund).GetProperty(nameof(Repository.Entities.AbpFund.EmploymentContribution))!, typeof(ModelAbpFund).GetProperty(nameof(ModelAbpFund.EmploymentContribution))! ),
            ( typeof(Repository.Entities.AbpFund).GetProperty(nameof(Repository.Entities.AbpFund.Franchise))!, typeof(ModelAbpFund).GetProperty(nameof(ModelAbpFund.Franchise))! ),
            ( typeof(Repository.Entities.AbpFund).GetProperty(nameof(Repository.Entities.AbpFund.FranchiseUpToAge40))!, typeof(ModelAbpFund).GetProperty(nameof(ModelAbpFund.FranchiseUpToAge40))! ),
            ( typeof(Repository.Entities.AbpFund).GetProperty(nameof(Repository.Entities.AbpFund.FranchiseUpToAge50))!, typeof(ModelAbpFund).GetProperty(nameof(ModelAbpFund.FranchiseUpToAge50))! )
        ];

    protected override IEnumerable<(PropertyInfo Entity, PropertyInfo ModelEntity)> Properties => properties;
}
