using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Mappers;

internal class BaseForCalculationBasePayrollComponentProfile : Profile
{
    public BaseForCalculationBasePayrollComponentProfile()
    {
        #region GET

        CreateMap<Repository.Entities.BaseForCalculationBasePayrollComponent, BaseForCalculationBasePayrollComponentModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.MapFrom(src => src.InheritanceLevel))
            .ForMember(dst => dst.BaseForCalculation, opt => opt.MapFrom(src => src.BaseForCalculationId))
            .ForMember(dst => dst.PayrollComponent, opt => opt.MapFrom(src => src.Component))
            .ForMember(dst => dst.Year, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.StartPayrollPeriod, opt => opt.MapFrom(src => src.PayrollPeriod))
            .ForMember(dst => dst.Origin, opt => opt.MapFrom(src => src.CtBaseOrigin))
            .ForMember(dst => dst.DefinedAtLevel, opt => opt.MapFrom(src => src));

        CreateMap<Repository.Entities.BaseForCalculationBasePayrollComponent, BaseForCalculationBasePayrollComponentDefinedAtLevelModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.ComponentDefinedAtLevel))
            .ForMember(dst => dst.Origin, opt => opt.MapFrom(src => src.BaseOriginDefinedAtLevel));

        #endregion

        #region POST

        CreateMap<BaseForCalculationBasePayrollComponentPostModel, ModelBaseForCalculationBasePayrollComponent>()
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.MapFrom((_, _, _, context) => context.TryGetItems(out var items) ? (int)items[nameof(IInheritanceEntity.InheritanceLevelId)] : default))
            .ForMember(dst => dst.YearId, opt => opt.MapFrom(src => src.Year))
            .ForMember(dst => dst.BaseForCalculationId, opt => opt.MapFrom(src => src.BaseForCalculationId))
            .ForMember(dst => dst.ComponentId, opt => opt.MapFrom(src => src.PayrollComponent.Key))
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.MapFrom(src => src.StartPayrollPeriod.PeriodNumber))
            .ForMember(dst => dst.Origin, opt => opt.MapFrom(src => src.Origin.Key))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore());

        #endregion

        #region PATCH

        CreateMap<BaseForCalculationBasePayrollComponentPatchModel,
                Repository.Entities.BaseForCalculationBasePayrollComponent>()
            .ForMember(dst => dst.BaseOrigin, opt => opt.MapFrom(src => src.Origin))

            // Ignore the primary key properties as they are not needed for PATCH operations
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.Ignore())
            .ForMember(dst => dst.YearId, opt => opt.Ignore())
            .ForMember(dst => dst.BaseForCalculationId, opt => opt.Ignore())
            .ForMember(dst => dst.ComponentId, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.Ignore())

            // Ignore the DefinedAtLevel properties as they are not needed for PATCH operations
            .ForMember(dst => dst.BaseForCalculationDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.ComponentDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriodDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.BaseOriginDefinedAtLevel, opt => opt.Ignore())

            // Ignore the navigation properties as they are not needed for PATCH operations
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.Year, opt => opt.Ignore())
            .ForMember(dst => dst.BaseForCalculation, opt => opt.Ignore())
            .ForMember(dst => dst.Component, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.ModelComponent, opt => opt.Ignore())
            .ForMember(dst => dst.CtBaseOrigin, opt => opt.Ignore());

        CreateMap<Repository.Entities.BaseForCalculationBasePayrollComponent, ModelBaseForCalculationBasePayrollComponent>()
            .ForMember(dst => dst.Origin, opt => opt.MapFrom(src => src.CtBaseOrigin.Code))

            // Mapping the primary key properties
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.MapFrom(src => src.InheritanceLevelId))
            .ForMember(dst => dst.YearId, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.BaseForCalculationId, opt => opt.MapFrom(src => src.BaseForCalculationId))
            .ForMember(dst => dst.ComponentId, opt => opt.MapFrom(src => src.ComponentId))
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.MapFrom(src => src.PayrollPeriodId))

            // Ignore the navigation properties as they are not needed for PATCH operations
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore());

        #endregion
        
        // For cloning objects with AutoMapper
        CreateMap<ModelBaseForCalculationBasePayrollComponent, ModelBaseForCalculationBasePayrollComponent>()
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore());

        // Add cloning mapping for post model
        CreateMap<BaseForCalculationBasePayrollComponentPostModel, BaseForCalculationBasePayrollComponentPostModel>();
    }
}