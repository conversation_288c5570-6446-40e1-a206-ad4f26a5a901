using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Interfaces;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Infrastructure.Validators;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using Vsp.PayrollConfiguration.Infrastructure.Constants;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Validators;

internal class InsertBaseForCalculationBasePayrollComponentValidator
    : AbstractValidator<BaseForCalculationBasePayrollComponentPostModel>
{
    public InsertBaseForCalculationBasePayrollComponentValidator(
        ILoketContext loketContext,
        IMapper mapper,
        IBaseForCalculationBasePayrollComponentHelper baseForCalculationBasePayrollComponentHelper
    )
    {
        // Set cascade mode to stop on first validation failure to ensure staged validation
        // this.RuleLevelCascadeMode = CascadeMode.Stop;

        // Stage 1 & 2: Infrastructure validations (from base validator)
        Include(new InsertInheritanceEntityValidator<
            BaseForCalculationBasePayrollComponentPostModel,
            Repository.Entities.BaseForCalculationBasePayrollComponent,
            ModelBaseForCalculationBasePayrollComponent>(loketContext, mapper));

        // Rule 1: Chosen component must be available for the given base + period
        RuleFor(m => m.PayrollComponent.Key)
            .MustAsync(async (model, componentId, token) =>
            {
                return await baseForCalculationBasePayrollComponentHelper
                    .GetAvailableBasePayrollComponents(model.BaseForCalculationGuidId, model.StartPayrollPeriod.PeriodNumber!.Value)
                    .Where(c => c.ComponentId == componentId)
                    .AnyAsync(token);
            })
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_PayrollComponent_Invalid)
            .WithMessage("payrollComponent.key is invalid");
    }
}