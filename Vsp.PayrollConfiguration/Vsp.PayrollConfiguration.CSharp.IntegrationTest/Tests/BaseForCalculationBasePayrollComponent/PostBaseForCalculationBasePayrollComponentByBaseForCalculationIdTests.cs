using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.BaseForCalculationBasePayrollComponent.Constants;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Models;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculationBasePayrollComponent;

[Collection(EntityNames.BaseForCalculationBasePayrollComponent)]
public class PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculationBasePayrollComponent";
    protected override bool UseTransaction => true;

    private static readonly Guid QA_BFC_BasePayrollComponent_BFC1_POST_CLA = Guid.Parse("000009f4-07e9-0001-0100-000000000000"); // InheritanceLevel 2548, year 2025, BaseForCalculation 1
    private static readonly Guid QA_BFC_BasePayrollComponent_BFC1_POST_WM = Guid.Parse("000009f6-07e9-0001-0100-000000000000"); // InheritanceLevel 2550, year 2025, BaseForCalculation 1
    private static readonly Guid QA_BFC_BasePayrollComponent_BFC1_POST_PA = Guid.Parse("000009fa-07e9-0001-0100-000000000000"); // InheritanceLevel 2554, year 2025, BaseForCalculation 1
    
    #region OK

    [Fact]
    public async Task Ok_QA_BaseForCalculationBasePayrollComponent_POST_CLA() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_CLA.ToString())}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationBasePayrollComponentPostModel(model =>
                {
                    model.Year = 2025;
                    model.PayrollComponent = new KeyModel { Key = 257 };
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task Ok_QA_BaseForCalculationBasePayrollComponent_POST_WM() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_WM.ToString())}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationBasePayrollComponentPostModel(model =>
                {
                    model.Year = 2025;
                    model.PayrollComponent = new KeyModel { Key = 257 };
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task Ok_QA_BaseForCalculationBasePayrollComponent_POST_PA() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_PA.ToString())}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationBasePayrollComponentPostModel(model =>
                {
                    model.Year = 2025;
                    model.PayrollComponent = new KeyModel { Key = 257 };
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    #endregion

    #region ModelValidation

    [Fact]
    public async Task BadRequest_ModelValidation_Null() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_CLA.ToString())}",
                Method = HttpMethod.Post,
                Body = ""
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task BadRequest_ModelValidation_Properties_Null() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_CLA.ToString())}",
                Method = HttpMethod.Post,
                Body = new BaseForCalculationBasePayrollComponentPostModel { Year = null, PayrollComponent = null!, StartPayrollPeriod = null! }
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    [Fact]
    public async Task BadRequest_ModelValidation_SubProperties_Null() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_CLA.ToString())}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationBasePayrollComponentPostModel(m =>
                {
                    m.PayrollComponent = new KeyModel { Key = null };
                    m.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = null };
                })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    [Theory]
    [InlineData(1899, 257, 1)] // Year too low
    [InlineData(10000, 257, 1)] // Year too high
    [InlineData(2025, 257, 0)] // PeriodNumber too low
    [InlineData(2025, 257, 54)] // PeriodNumber too high
    public async Task BadRequest_ModelValidation_Boundaries(int year, int payrollComponentKey, int periodNumber) =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_CLA.ToString())}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationBasePayrollComponentPostModel(m =>
                {
                    m.Year = year;
                    m.PayrollComponent = new KeyModel { Key = payrollComponentKey };
                    m.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = periodNumber };
                })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    #endregion

    #region MessageCodes

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_PayrollComponent_Invalid"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_PayrollComponent_Invalid() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_CLA.ToString())}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationBasePayrollComponentPostModel(m =>
                {
                    m.PayrollComponent = new KeyModel { Key = 9999 }; // Invalid component
                })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_PayrollPeriodNumber_Invalid"/>
    /// This test should use a period number that exists in the infrastructure but is invalid for the specific base for calculation
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_PayrollPeriodNumber_Invalid() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_CLA.ToString())}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationBasePayrollComponentPostModel(m =>
                {
                    m.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 12 }; // Period that exists in infrastructure but invalid for this specific base
                })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_Origin_Invalid"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Origin_Invalid() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_CLA.ToString())}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationBasePayrollComponentPostModel(m =>
                {
                    m.Origin = new KeyValueModel { Key = 9999 }; // Invalid origin
                })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_Year_DoesNotExist"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Year_DoesNotExist() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_CLA.ToString())}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationBasePayrollComponentPostModel(m =>
                {
                    m.Year = 2024; // Year that doesn't exist on this inheritance level
                    m.PayrollComponent = new KeyModel { Key = 257 };
                })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_PayrollPeriod_DoesNotExist"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_PayrollPeriod_DoesNotExist() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_CLA.ToString())}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationBasePayrollComponentPostModel(m =>
                {
                    m.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 13 }; // Period that doesn't exist
                })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_PayrollPeriod_FirstPeriodDoesNotExist"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_PayrollPeriod_FirstPeriodDoesNotExist() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_CLA.ToString())}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationBasePayrollComponentPostModel(m =>
                {
                    m.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 2 }; // Cannot add period 2 if period 1 doesn't exist
                })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    /// <summary>
    /// Insert first entity, then try to insert the same entity again
    ///  See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_Entity_AlreadyExists_CurrentInheritanceLevel"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Entity_AlreadyExists_CurrentInheritanceLevel()
    {
        var postModel = GetBaseForCalculationBasePayrollComponentPostModel(m =>
        {
            m.Year = 2025;
            m.PayrollComponent = new KeyModel { Key = 257 };
            m.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
        });
        var postUri = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_CLA.ToString())}";

        // Insert first time
        await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Try to insert same entity again (should fail)
        await VerifyCallAsync(
            new Request { Url = postUri, Method = HttpMethod.Post, Body = postModel },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });
    }

    ///  <summary>
    ///  Test an insert on parent (CLA), then tries to insert on child (WM) with same component/year/period
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_Entity_AlreadyExists_ParentInheritanceLevel"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Entity_AlreadyExists_ParentInheritanceLevel()
    {
       
        var postModel = GetBaseForCalculationBasePayrollComponentPostModel(m =>
        {
            m.Year = 2025;
            m.PayrollComponent = new KeyModel { Key = 257 };
            m.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
        });
        var postUriCLA = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_CLA.ToString())}";
        var postUriWM = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_WM.ToString())}";

        await CallApiAsync(HttpMethod.Post, postUriCLA, postModel, HttpStatusCode.Created);

        await VerifyCallAsync(
            new Request { Url = postUriWM, Method = HttpMethod.Post, Body = postModel },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });
    }

    #endregion

    #region Post model helpers

    private BaseForCalculationBasePayrollComponentPostModel GetBaseForCalculationBasePayrollComponentPostModel(Action<BaseForCalculationBasePayrollComponentPostModel>? setNewPropertyValues = null)
    {
        var postModel = new BaseForCalculationBasePayrollComponentPostModel
        {
            Year = 2025,
            PayrollComponent = new KeyModel { Key = 257 },
            StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 },
            Origin = new KeyValueModel { Key = 2 }
        };

        setNewPropertyValues?.Invoke(postModel);
        return postModel;
    }
    
    #endregion
}