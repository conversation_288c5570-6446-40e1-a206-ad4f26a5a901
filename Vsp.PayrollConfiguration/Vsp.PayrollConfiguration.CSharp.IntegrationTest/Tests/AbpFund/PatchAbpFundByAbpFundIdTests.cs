using Vsp.PayrollConfiguration.AbpFund.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.AbpFund.Models;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.AbpFund;

[Collection(EntityNames.AbpFund)]
public class PatchAbpFundByAbpFundIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "AbpFund";
    protected override bool UseTransaction => true;
    
    private const string QA_AbpFund_PATCH_CLA = "000009fc-07e9-0001-0100-000000000000"; // AbpFund for CLA - Year 2025 - Age 50
    private const string QA_AbpFund_PATCH_WM = "000009fe-07e9-0001-0100-000000000000"; // AbpFund for WM - Year 2025 - Age 30
    private const string QA_AbpFund_PATCH_PA = "000009fd-07e9-0001-0100-000000000000"; // AbpFund for PA - Year 2025 - Age 65
    
    #region OK Tests

    [Fact]
    public async Task Ok_QA_AbpFund_PATCH_CLA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new AbpFundPatchModel
                {
                    TotalContribution = 5.123m,
                    EmploymentContribution = 3.567m,
                    Franchise = 12345.67m,
                    FranchiseUpToAge40 = 23456.78m,
                    FranchiseUpToAge50 = 34567.89m
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    [Fact]
    public async Task Ok_QA_AbpFund_PATCH_WM_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_WM),
                Method = HttpMethod.Patch,
                Body = new AbpFundPatchModel
                {
                    EmploymentContribution = 1.000m,
                    TotalContribution = 2.000m,
                    Franchise = 12345.67m,
                    FranchiseUpToAge40 = 4.00m,
                    FranchiseUpToAge50 = 5.00m
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    [Fact]
    public async Task Ok_QA_AbpFund_PATCH_PA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_PA),
                Method = HttpMethod.Patch,
                Body = new AbpFundPatchModel
                {
                    EmploymentContribution = 1m,
                    TotalContribution = 5.123m,
                    Franchise = 3m,
                    FranchiseUpToAge40 = 4m,
                    FranchiseUpToAge50 = 5m
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    #endregion

    #region BadRequest - ModelStateValidation

    [Fact]
    public async Task BadRequest_ModelValidation_NoPatchModel()
        => await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = null
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Fact]
    public async Task BadRequest_ModelValidation_MissingProperties()
        => await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new object()
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Fact]
    public async Task BadRequest_ModelValidation_DefaultValueProperties()
        => await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new AbpFundPatchModel()
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Theory]
    [InlineData(-0.999)]
    [InlineData(100)]
    public async Task BadRequest_ModelValidation_TotalContribution_OutOfRange(decimal value)
        => await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new AbpFundPatchModel
                {
                    TotalContribution = value,
                    EmploymentContribution = 3.567m,
                    Franchise = 12345.67m,
                    FranchiseUpToAge40 = 23456.78m,
                    FranchiseUpToAge50 = 34567.89m
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );
    
    [Theory]
    [InlineData(-0.999)]
    [InlineData(100)]
    public async Task BadRequest_ModelValidation_EmploymentContribution_OutOfRange(decimal value)
        => await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new AbpFundPatchModel
                {
                    TotalContribution = 5.123m,
                    EmploymentContribution = value,
                    Franchise = 12345.67m,
                    FranchiseUpToAge40 = 23456.78m,
                    FranchiseUpToAge50 = 34567.89m
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );
    
    
    [Theory]
    [InlineData(-0.99)]
    [InlineData(1000000)]
    public async Task BadRequest_ModelValidation_Franchise_OutOfRange(decimal value)
        => await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new AbpFundPatchModel
                {
                    TotalContribution = 5.123m,
                    EmploymentContribution = 3.567m,
                    Franchise = value,
                    FranchiseUpToAge40 = 23456.78m,
                    FranchiseUpToAge50 = 34567.89m
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );
    
    [Theory]
    [InlineData(-0.99)]
    [InlineData(1000000)]
    public async Task BadRequest_ModelValidation_FranchiseUpToAge40_OutOfRange(decimal value)
        => await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new AbpFundPatchModel
                {
                    TotalContribution = 5.123m,
                    EmploymentContribution = 3.567m,
                    Franchise = 12345.67m,
                    FranchiseUpToAge40 = value,
                    FranchiseUpToAge50 = 34567.89m
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );
    
    [Theory]
    [InlineData(-0.99)]
    [InlineData(1000000)]
    public async Task BadRequest_ModelValidation_FranchiseUpToAge50_OutOfRange(decimal value)
        => await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new AbpFundPatchModel
                {
                    TotalContribution = 5.123m,
                    EmploymentContribution = 3.567m,
                    Franchise = 12345.67m,
                    FranchiseUpToAge40 = 23456.78m,
                    FranchiseUpToAge50 = value
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );
    
    [Fact]
    public async Task BadRequest_ModelValidation_TotalContribution_TooManyDecimals()
        => await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new AbpFundPatchModel
                {
                    TotalContribution = 4.1234m,
                    EmploymentContribution = 3.567m,
                    Franchise = 12345.67m,
                    FranchiseUpToAge40 = 23456.78m,
                    FranchiseUpToAge50 = 34567.89m
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );
    
    [Fact]
    public async Task BadRequest_ModelValidation_EmploymentContribution_TooManyDecimals()
        => await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new AbpFundPatchModel
                {
                    TotalContribution = 5.123m,
                    EmploymentContribution = 2.3456m,
                    Franchise = 12345.67m,
                    FranchiseUpToAge40 = 23456.78m,
                    FranchiseUpToAge50 = 34567.89m
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );
    
    
    [Fact]
    public async Task BadRequest_ModelValidation_Franchise_TooManyDecimals()
        => await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new AbpFundPatchModel
                {
                    TotalContribution = 5.123m,
                    EmploymentContribution = 3.567m,
                    Franchise = 4.234m,
                    FranchiseUpToAge40 = 23456.78m,
                    FranchiseUpToAge50 = 34567.89m
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );
    
    [Fact]
    public async Task BadRequest_ModelValidation_FranchiseUpToAge40_TooManyDecimals()
        => await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new AbpFundPatchModel
                {
                    TotalContribution = 5.123m,
                    EmploymentContribution = 3.567m,
                    Franchise = 12345.67m,
                    FranchiseUpToAge40 = 6.234m,
                    FranchiseUpToAge50 = 34567.89m
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );
    
    [Fact]
    public async Task BadRequest_ModelValidation_FranchiseUpToAge50_TooManyDecimals()
        => await VerifyCallAsync(
            new Request
            {
                Url = AbpFundRoutes.PatchAbpFundByAbpFundIdAsync
                    .Replace("{abpFundId:guid}", QA_AbpFund_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new AbpFundPatchModel
                {
                    TotalContribution = 5.123m,
                    EmploymentContribution = 3.567m,
                    Franchise = 12345.67m,
                    FranchiseUpToAge40 = 23456.78m,
                    FranchiseUpToAge50 = 6.234m
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );
    #endregion
}