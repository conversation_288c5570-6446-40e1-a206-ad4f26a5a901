{"content": null, "messages": [{"code": 0, "description": "Cannot add data for a later payroll period in this year, because there is no data for the first payroll period yet.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_Insert_PayrollPeriod_FirstPeriodDoesNotExist", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}, {"code": 0, "description": "payrollComponent.key is invalid", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_PayrollComponent_Invalid", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}