{"content": null, "messages": [{"code": 0, "description": "Payroll period does not exist on current inheritance level.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_Insert_PayrollPeriod_DoesNotExist", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}, {"code": 0, "description": "payrollComponent.key is invalid", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_PayrollComponent_Invalid", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}